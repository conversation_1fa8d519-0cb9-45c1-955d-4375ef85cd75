import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import {
  Type,
  Image as ImageIcon,
  Pencil,
  Square,
  Circle,
  Palette,
} from 'lucide-react';

interface PdfEditSidebarProps {
  currentTool: string;
  setCurrentTool: (tool: string) => void;
  onPropertyChange: (property: string, value: any) => void;
  fontSize: number;
  color: string;
  brushSize: number;
  borderWidth: number;
  fillColor: string;
  textContent: string;
  setTextContent: (text: string) => void;
}

const PdfEditSidebar: React.FC<PdfEditSidebarProps> = ({
  currentTool,
  setCurrentTool,
  onPropertyChange,
  fontSize,
  color,
  brushSize,
  borderWidth,
  fillColor,
  textContent,
  setTextContent,
}) => {
  const handleToolClick = (tool: string) => {
    setCurrentTool(currentTool === tool ? '' : tool);
  };

  return (
    <div className="w-64 border-l bg-white p-4 flex flex-col gap-4">
      <div>
        <h3 className="font-semibold mb-2">Tools</h3>
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant={currentTool === 'text' ? 'default' : 'outline'}
            className="w-full"
            onClick={() => handleToolClick('text')}
          >
            <Type className="h-4 w-4 mr-2" />
            Text
          </Button>
          <Button
            variant={currentTool === 'image' ? 'default' : 'outline'}
            className="w-full"
            onClick={() => handleToolClick('image')}
          >
            <ImageIcon className="h-4 w-4 mr-2" />
            Image
          </Button>
          <Button
            variant={currentTool === 'draw' ? 'default' : 'outline'}
            className="w-full"
            onClick={() => handleToolClick('draw')}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Draw
          </Button>
          <Button
            variant={currentTool === 'rectangle' ? 'default' : 'outline'}
            className="w-full"
            onClick={() => handleToolClick('rectangle')}
          >
            <Square className="h-4 w-4 mr-2" />
            Rectangle
          </Button>
          <Button
            variant={currentTool === 'circle' ? 'default' : 'outline'}
            className="w-full"
            onClick={() => handleToolClick('circle')}
          >
            <Circle className="h-4 w-4 mr-2" />
            Circle
          </Button>
        </div>
      </div>

      {currentTool && (
        <div className="border-t pt-4">
          <h3 className="font-semibold mb-2">Properties</h3>
          
          {currentTool === 'text' && (
            <div className="space-y-4">
              <div>
                <Label>Text Content</Label>
                <Input 
                  value={textContent}
                  onChange={(e) => setTextContent(e.target.value)}
                  placeholder="Enter text to add..."
                />
              </div>
              <div>
                <Label>Font Size</Label>
                <Input 
                  type="number" 
                  min={8} 
                  max={72} 
                  value={fontSize}
                  onChange={(e) => onPropertyChange('fontSize', parseInt(e.target.value))}
                />
              </div>
              <div>
                <Label>Color</Label>
                <div className="flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  <Input 
                    type="color" 
                    className="w-full h-8"
                    value={color}
                    onChange={(e) => onPropertyChange('color', e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}

          {currentTool === 'draw' && (
            <div className="space-y-4">
              <div>
                <Label>Brush Size</Label>
                <Slider
                  value={[brushSize]}
                  max={20}
                  min={1}
                  step={1}
                  className="mt-2"
                  onValueChange={(value) => onPropertyChange('brushSize', value[0])}
                />
              </div>
              <div>
                <Label>Color</Label>
                <div className="flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  <Input 
                    type="color" 
                    className="w-full h-8"
                    value={color}
                    onChange={(e) => onPropertyChange('color', e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}

          {(currentTool === 'rectangle' || currentTool === 'circle') && (
            <div className="space-y-4">
              <div>
                <Label>Border Width</Label>
                <Slider
                  value={[borderWidth]}
                  max={10}
                  min={1}
                  step={1}
                  className="mt-2"
                  onValueChange={(value) => onPropertyChange('borderWidth', value[0])}
                />
              </div>
              <div>
                <Label>Border Color</Label>
                <div className="flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  <Input 
                    type="color" 
                    className="w-full h-8"
                    value={color}
                    onChange={(e) => onPropertyChange('color', e.target.value)}
                  />
                </div>
              </div>
              <div>
                <Label>Fill Color</Label>
                <div className="flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  <Input 
                    type="color" 
                    className="w-full h-8"
                    value={fillColor}
                    onChange={(e) => onPropertyChange('fillColor', e.target.value)}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PdfEditSidebar;
