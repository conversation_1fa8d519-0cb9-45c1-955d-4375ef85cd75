export type AuthError = {
  code: string;
  message: string;
};

export const getAuthErrorMessage = (error: any): string => {
  const errorCode = error?.code || '';
  
  switch (errorCode) {
    // Sign In Errors
    case 'auth/invalid-credential':
      return 'Invalid email or password';
    case 'auth/user-not-found':
      return 'No account found with this email';
    case 'auth/wrong-password':
      return 'Incorrect password';
    case 'auth/invalid-email':
      return 'Invalid email address';
    case 'auth/user-disabled':
      return 'This account has been disabled';

    // Sign Up Errors
    case 'auth/email-already-in-use':
      return 'An account already exists with this email';
    case 'auth/weak-password':
      return 'Password should be at least 6 characters';
    case 'auth/operation-not-allowed':
      return 'Email/password sign up is not enabled';

    // Google Sign In Errors
    case 'auth/popup-closed-by-user':
      return 'Sign in was cancelled';
    case 'auth/cancelled-popup-request':
      return 'Only one sign in window can be open at a time';
    case 'auth/popup-blocked':
      return 'Sign in popup was blocked by your browser';

    // Generic Errors
    case 'auth/network-request-failed':
      return 'Network error occurred. Please check your connection';
    case 'auth/too-many-requests':
      return 'Too many attempts. Please try again later';
    
    default:
      return 'An unexpected error occurred. Please try again';
  }
};