"use client";
import {
  DragDropElementLayout,
  useEmailTemplate,
} from "@/app/email-editor/Provider";
import { useState, JSX } from "react";
import ButtonElement from "./Elements/ButtonElement";
import TextElement from "./Elements/TextElement";
import ImageElement from "./Elements/ImgElement";
import SocialElement from "./Elements/SocialElement";
import LogoElement from "./Elements/LogoElement";
import DividerElement from "./Elements/DividerElement";
import { useSelectedElement } from "@/app/email-editor/Provider";
import { Button } from "../ui/button";
import { Trash2 } from "lucide-react";

interface ColumnLayoutProps {
  layout: {
    numOfCol: number;
    id: number;
    [index: number]: DragElement | undefined; // Store elements directly
  };
}

interface DragOverState {
  index: number;
  columnId: number;
}

interface DragElement {
  type: string;
  [key: string]: any; // Allow additional properties
}

const ColumnLayout: React.FC<ColumnLayoutProps> = ({ layout }) => {
  const { emailTemplate, setEmailTemplate } = useEmailTemplate();

  const { dragElementlayout } = DragDropElementLayout();
  const { selectedElement, setSelectedElement } = useSelectedElement() as {
    selectedElement: { layout: { id: number }; index: number } | null;
    setSelectedElement: (element: { layout: { id: number }; index: number } | null) => void;
  };
  const [dragOver, setDragOver] = useState<DragOverState | null>(null);

  const deleteSelectedElement = () => {
    if (!selectedElement) return;
    
    setEmailTemplate((prevTemplate: any) =>
      prevTemplate.map((col: any) =>
        col.id === selectedElement.layout.id
          ? { ...col, [selectedElement.index]: undefined }
          : col
      )
    );
    setSelectedElement(null);
  };

  // Handle drag over event
  const onDragOverHandle = (
    event: React.DragEvent<HTMLDivElement>,
    index: number
  ) => {
    event.preventDefault();
    setDragOver({
      index: index,
      columnId: layout?.id,
    });
  };

  // Handle drop event
  const onDropHandle = () => {
    if (!dragOver || !dragElementlayout?.dragElement) return;
    const index = dragOver.index;
    setEmailTemplate((prevTemplate: any) =>
      prevTemplate.map((col: any) =>
        col.id === layout?.id
          ? { ...col, [index]: dragElementlayout.dragElement }
          : col
      )
    );

    console.log("Updated Email Template:", emailTemplate);
    setDragOver(null);
  };

  // Handle drag leave event
  const onDragLeaveHandle = () => {
    setDragOver(null);
  };

  // Function to get element type for display
  const getElementComponent = (
    element: DragElement | undefined
  ): JSX.Element | string => {
    if (element?.type === "button") {
      return (
        <ButtonElement
          content={element.content}
          url={element.url}
          {...element}
        />
      );
    }
    if (element?.type === "text") {
      return <TextElement content={element.content} {...element} />;
    }
    if (element?.type === "image") {
      return <ImageElement imageUrl={element.imageUrl} {...element} />;
    }
    if (element?.type === "logo") {
      return <LogoElement imageUrl={element.imageUrl} {...element} />;
    }
    if (element?.type === "divider") {
      return <DividerElement />;
    }

    if (element?.type === "icons ") {
      return <SocialElement socialLinks={element.socialLinks} {...element} />;
    }
    return "Not avalible";
  };

  return (
    <div className="relative">
      {selectedElement?.layout.id === layout.id && (
        <div className="absolute -right-10 top-0 flex flex-col gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={deleteSelectedElement}
            className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
          >
            <Trash2 size={16} />
          </Button>
        </div>
      )}
      
      <div className={`grid grid-cols-${layout.numOfCol} gap-4`}>
        {Array.from({ length: layout.numOfCol }).map((_, index) => (
          <div
            key={index}
            className={`min-h-[100px] p-4 ${
              index === dragOver?.index && dragOver?.columnId === layout.id
                ? "bg-blue-100"
                : "bg-gray-50"
            } ${
              selectedElement?.layout.id === layout.id &&
              selectedElement?.index === index
                ? "ring-2 ring-blue-500"
                : ""
            }`}
            onDragOver={(e) => onDragOverHandle(e, index)}
            onDrop={onDropHandle}
            onDragLeave={onDragLeaveHandle}
            onClick={() => setSelectedElement({ layout, index })}
          >
            {layout[index] ? (
              <div className="relative group">
                {getElementComponent(layout[index] as DragElement)}
              </div>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-400">
                Drag and Drop here
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ColumnLayout;
