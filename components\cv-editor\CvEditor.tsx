'use client';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, 
  SelectItem 
} from "@/components/ui/select"
import { Download, Plus, Trash } from 'lucide-react';
import { CvData, cvTemplates, defaultCvData } from '@/lib/cv-templates';
import { nanoid } from 'nanoid';
import CvPreview from './CvPreview';

export default function CvEditor() {
  const [cvData, setCvData] = useState<CvData>(defaultCvData);
  
  const updatePersonalInfo = (field: keyof CvData['personalInfo'], value: string) => {
    setCvData(prev => ({
      ...prev,
      personalInfo: { ...prev.personalInfo, [field]: value }
    }));
  };

  const addEducation = () => {
    setCvData(prev => ({
      ...prev,
      education: [...prev.education, { id: nanoid(), degree: '', institution: '', year: '' }]
    }));
  };

  const updateEducation = (id: string, field: string, value: string) => {
    setCvData(prev => ({
      ...prev,
      education: prev.education.map(edu => 
        edu.id === id ? { ...edu, [field]: value } : edu
      )
    }));
  };

  const removeEducation = (id: string) => {
    setCvData(prev => ({
      ...prev,
      education: prev.education.filter(edu => edu.id !== id)
    }));
  };

  const addExperience = () => {
    setCvData(prev => ({
      ...prev,
      experience: [...prev.experience, {
        id: nanoid(),
        position: '',
        company: '',
        startDate: '',
        endDate: '',
        description: ''
      }]
    }));
  };

  const updateExperience = (id: string, field: string, value: string) => {
    setCvData(prev => ({
      ...prev,
      experience: prev.experience.map(exp => 
        exp.id === id ? { ...exp, [field]: value } : exp
      )
    }));
  };

  const removeExperience = (id: string) => {
    setCvData(prev => ({
      ...prev,
      experience: prev.experience.filter(exp => exp.id !== id)
    }));
  };

  const addSkill = () => {
    setCvData(prev => ({
      ...prev,
      skills: [...prev.skills, { id: nanoid(), name: '', level: 'Intermediate' }]
    }));
  };

  const updateSkill = (id: string, field: string, value: string) => {
    setCvData(prev => ({
      ...prev,
      skills: prev.skills.map(skill => 
        skill.id === id ? { ...skill, [field]: value } : skill
      )
    }));
  };

  const removeSkill = (id: string) => {
    setCvData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill.id !== id)
    }));
  };

  const downloadAsDocx = async () => {
    try {
      const response = await fetch('/api/cv/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cvData),
      });
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${cvData.personalInfo.fullName.toLowerCase().replace(/\s+/g, '-')}-cv.docx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading CV:', error);
    }
  };

  return (
    <div className="flex gap-6 max-w-7xl mx-auto p-6">
      <div className="w-1/2 space-y-8">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">CV Editor</h1>
          <div className="flex gap-2">
            <Select
              value={cvData.template}
              onValueChange={(value: string) => setCvData(prev => ({ ...prev, template: value }))}
            >
              {cvTemplates.map(template => (
                <SelectItem key={template.id} value={template.id}>
                  {template.name}
                </SelectItem>
              ))}
            </Select>
            <Button onClick={downloadAsDocx}>
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
          </div>
        </div>

        {/* Personal Information */}
        <section className="space-y-4">
          <h2 className="text-xl font-semibold">Personal Information</h2>
          <div className="grid gap-4">
            <Input
              placeholder="Full Name"
              value={cvData.personalInfo.fullName}
              onChange={(e) => updatePersonalInfo('fullName', e.target.value)}
            />
            <Input
              placeholder="Email"
              type="email"
              value={cvData.personalInfo.email}
              onChange={(e) => updatePersonalInfo('email', e.target.value)}
            />
            <Input
              placeholder="Phone"
              value={cvData.personalInfo.phone}
              onChange={(e) => updatePersonalInfo('phone', e.target.value)}
            />
            <Input
              placeholder="Address"
              value={cvData.personalInfo.address}
              onChange={(e) => updatePersonalInfo('address', e.target.value)}
            />
            <Textarea
              placeholder="Professional Summary"
              value={cvData.personalInfo.summary}
              onChange={(e) => updatePersonalInfo('summary', e.target.value)}
            />
          </div>
        </section>

        {/* Education */}
        <section className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Education</h2>
            <Button onClick={addEducation} variant="outline" size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Add Education
            </Button>
          </div>
          {cvData.education.map((edu) => (
            <div key={edu.id} className="grid gap-4 p-4 border rounded-lg relative">
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() => removeEducation(edu.id)}
              >
                <Trash className="w-4 h-4" />
              </Button>
              <Input
                placeholder="Degree"
                value={edu.degree}
                onChange={(e) => updateEducation(edu.id, 'degree', e.target.value)}
              />
              <Input
                placeholder="Institution"
                value={edu.institution}
                onChange={(e) => updateEducation(edu.id, 'institution', e.target.value)}
              />
              <Input
                placeholder="Year"
                value={edu.year}
                onChange={(e) => updateEducation(edu.id, 'year', e.target.value)}
              />
            </div>
          ))}
        </section>

        {/* Experience */}
        <section className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Experience</h2>
            <Button onClick={addExperience} variant="outline" size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Add Experience
            </Button>
          </div>
          {cvData.experience.map((exp) => (
            <div key={exp.id} className="grid gap-4 p-4 border rounded-lg relative">
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() => removeExperience(exp.id)}
              >
                <Trash className="w-4 h-4" />
              </Button>
              <Input
                placeholder="Position"
                value={exp.position}
                onChange={(e) => updateExperience(exp.id, 'position', e.target.value)}
              />
              <Input
                placeholder="Company"
                value={exp.company}
                onChange={(e) => updateExperience(exp.id, 'company', e.target.value)}
              />
              <div className="grid grid-cols-2 gap-4">
                <Input
                  placeholder="Start Date"
                  value={exp.startDate}
                  onChange={(e) => updateExperience(exp.id, 'startDate', e.target.value)}
                />
                <Input
                  placeholder="End Date"
                  value={exp.endDate}
                  onChange={(e) => updateExperience(exp.id, 'endDate', e.target.value)}
                />
              </div>
              <Textarea
                placeholder="Description"
                value={exp.description}
                onChange={(e) => updateExperience(exp.id, 'description', e.target.value)}
              />
            </div>
          ))}
        </section>

        {/* Skills */}
        <section className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Skills</h2>
            <Button onClick={addSkill} variant="outline" size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Add Skill
            </Button>
          </div>
          {cvData.skills.map((skill) => (
            <div key={skill.id} className="flex gap-4 items-center">
              <Input
                placeholder="Skill"
                value={skill.name}
                onChange={(e) => updateSkill(skill.id, 'name', e.target.value)}
                className="flex-1"
              />
              <Select
                value={skill.level}
                onValueChange={(value) => updateSkill(skill.id, 'level', value)}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Beginner">Beginner</SelectItem>
                  <SelectItem value="Intermediate">Intermediate</SelectItem>
                  <SelectItem value="Advanced">Advanced</SelectItem>
                  <SelectItem value="Expert">Expert</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => removeSkill(skill.id)}
              >
                <Trash className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </section>
      </div>

      {/* Preview */}
      <div className="w-1/2 bg-white rounded-lg shadow-lg">
        <CvPreview data={cvData} />
      </div>
    </div>
  );
}