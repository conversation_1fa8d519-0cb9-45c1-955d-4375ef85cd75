import { db } from './firebase';
import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  query, 
  where, 
  getDocs,
  DocumentData 
} from 'firebase/firestore';

// Collection references
const usersCollection = collection(db, 'users');

// User interface
interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  createdAt: Date;
  // Add any other user fields you need
}

// Create or update user
export async function createUser(userData: User) {
  const userRef = doc(usersCollection, userData.uid);
  await setDoc(userRef, {
    ...userData,
    createdAt: new Date(),
  });
}

// Get user by ID
export async function getUserById(uid: string) {
  const userRef = doc(usersCollection, uid);
  const userSnap = await getDoc(userRef);
  return userSnap.exists() ? userSnap.data() : null;
}

// Update user
export async function updateUser(uid: string, data: Partial<User>) {
  const userRef = doc(usersCollection, uid);
  await updateDoc(userRef, data);
}

// Get users by email
export async function getUserByEmail(email: string) {
  const q = query(usersCollection, where("email", "==", email));
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => doc.data());
}