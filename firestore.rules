rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Users collection rules
    match /users/{userId} {
      // Allow read/write if the user is authenticated and matches the document ID
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
    }

    // Documents collection rules (if you have one)
    match /documents/{documentId} {
      // Allow read/write if the user is authenticated
      allow read, write: if isAuthenticated();
    }

    // Default deny all other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
