import type { NextConfig } from "next";
import path from 'path';
import fs from 'fs';

const nextConfig: NextConfig = {
  transpilePackages: ['next-mdx-remote'],
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      'pdfjs-dist': require.resolve('pdfjs-dist/legacy/build/pdf'),
    };
    return config;
  },
  async afterBuild() {
    // Copy PDF.js worker to public directory
    const workerSrc = require.resolve('pdfjs-dist/build/pdf.worker.min.js');
    const workerDest = path.join(__dirname, 'public', 'pdf.worker.min.js');
    fs.copyFileSync(workerSrc, workerDest);
  },
};

export default nextConfig;
