{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "copy .\\node_modules\\pdfjs-dist\\build\\pdf.worker.mjs .\\public\\pdf.worker.min.js && next dev --turbopack", "build": "copy .\\node_modules\\pdfjs-dist\\build\\pdf.worker.mjs .\\public\\pdf.worker.min.js && next build", "start": "next start", "lint": "next lint", "postinstall": "copy .\\node_modules\\pdfjs-dist\\build\\pdf.worker.mjs .\\public\\pdf.worker.min.js"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.7", "@types/react-color": "^3.0.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "docx": "^9.3.0", "firebase": "^11.4.0", "lucide-react": "^0.474.0", "nanoid": "^5.1.3", "next": "15.1.6", "next-mdx-remote": "^5.0.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^4.10.38", "react": "^19.0.0", "react-color": "^2.19.3", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "rehype": "^13.0.2", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "remark-html": "^16.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}