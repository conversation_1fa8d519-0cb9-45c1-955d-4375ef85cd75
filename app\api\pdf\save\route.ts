import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { fileName, content } = body;

    if (!fileName || !content) {
      return NextResponse.json(
        { success: false, message: 'File name and content are required' },
        { status: 400 }
      );
    }

    // Create pdfs directory if it doesn't exist
    const pdfsDir = path.join(process.cwd(), 'public', 'pdfs');
    if (!fs.existsSync(pdfsDir)) {
      fs.mkdirSync(pdfsDir, { recursive: true });
    }

    // Convert content array back to Uint8Array
    const pdfBytes = new Uint8Array(content);

    // Save the PDF file
    const filePath = path.join(pdfsDir, fileName);
    fs.writeFileSync(filePath, pdfBytes);

    return NextResponse.json({
      success: true,
      message: 'PDF file saved successfully',
      path: `/pdfs/${fileName}`
    });
  } catch (error) {
    console.error('Error saving PDF file:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to save PDF file' },
      { status: 500 }
    );
  }
}
