'use client';
import { useState, useRef, useCallback } from 'react';
import PdfToolbar from '@/components/pdf-editor/PdfToolbar';
import PdfCanvas from '@/components/pdf-editor/PdfCanvas';
import PdfSidebar from '@/components/pdf-editor/PdfSidebar';
import PdfEditSidebar from '@/components/pdf-editor/PdfEditSidebar';
import { PDFDocument } from 'pdf-lib';
import { Button } from '@/components/ui/button';
import { Upload, Plus, Undo2, Redo2 } from 'lucide-react';

interface DrawingPath {
  id: string;
  type: string;
  points: { x: number; y: number }[];
  color: string;
  size: number;
  fillColor?: string;
  borderWidth?: number;
  text?: string;
}

interface EditorState {
  pdfDoc: PDFDocument | null;
  currentPage: number;
  scale: number;
  currentTool: string;
  fontSize: number;
  color: string;
  brushSize: number;
  borderWidth: number;
  fillColor: string;
}

export default function PdfEditor() {
  const [pdfDoc, setPdfDoc] = useState<PDFDocument | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [scale, setScale] = useState(1);
  const [fileName, setFileName] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [currentTool, setCurrentTool] = useState('');
  const [fontSize, setFontSize] = useState(16);
  const [color, setColor] = useState('#000000');
  const [brushSize, setBrushSize] = useState(2);
  const [borderWidth, setBorderWidth] = useState(1);
  const [fillColor, setFillColor] = useState('#ffffff');
  const [isModified, setIsModified] = useState(false);
  const [undoStack, setUndoStack] = useState<EditorState[]>([]);
  const [redoStack, setRedoStack] = useState<EditorState[]>([]);
  const [textContent, setTextContent] = useState('');
  const [selectedElement, setSelectedElement] = useState<DrawingPath | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const saveState = useCallback(() => {
    const currentState: EditorState = {
      pdfDoc,
      currentPage,
      scale,
      currentTool,
      fontSize,
      color,
      brushSize,
      borderWidth,
      fillColor,
    };
    setUndoStack(prev => [...prev, currentState]);
    setRedoStack([]);
    setIsModified(true);
  }, [pdfDoc, currentPage, scale, currentTool, fontSize, color, brushSize, borderWidth, fillColor]);

  const undo = () => {
    if (undoStack.length === 0) return;
    
    const previousState = undoStack[undoStack.length - 1];
    const currentState: EditorState = {
      pdfDoc,
      currentPage,
      scale,
      currentTool,
      fontSize,
      color,
      brushSize,
      borderWidth,
      fillColor,
    };
    
    setRedoStack(prev => [...prev, currentState]);
    setUndoStack(prev => prev.slice(0, -1));
    
    // Restore previous state
    setPdfDoc(previousState.pdfDoc);
    setCurrentPage(previousState.currentPage);
    setScale(previousState.scale);
    setCurrentTool(previousState.currentTool);
    setFontSize(previousState.fontSize);
    setColor(previousState.color);
    setBrushSize(previousState.brushSize);
    setBorderWidth(previousState.borderWidth);
    setFillColor(previousState.fillColor);
  };

  const redo = () => {
    if (redoStack.length === 0) return;
    
    const nextState = redoStack[redoStack.length - 1];
    const currentState: EditorState = {
      pdfDoc,
      currentPage,
      scale,
      currentTool,
      fontSize,
      color,
      brushSize,
      borderWidth,
      fillColor,
    };
    
    setUndoStack(prev => [...prev, currentState]);
    setRedoStack(prev => prev.slice(0, -1));
    
    // Restore next state
    setPdfDoc(nextState.pdfDoc);
    setCurrentPage(nextState.currentPage);
    setScale(nextState.scale);
    setCurrentTool(nextState.currentTool);
    setFontSize(nextState.fontSize);
    setColor(nextState.color);
    setBrushSize(nextState.brushSize);
    setBorderWidth(nextState.borderWidth);
    setFillColor(nextState.fillColor);
  };

  const handleToolChange = (tool: string) => {
    setCurrentTool(currentTool === tool ? '' : tool);
    saveState();
  };

  const handlePropertyChange = (property: string, value: any) => {
    switch (property) {
      case 'fontSize':
        setFontSize(value);
        break;
      case 'color':
        setColor(value);
        break;
      case 'brushSize':
        setBrushSize(value);
        break;
      case 'borderWidth':
        setBorderWidth(value);
        break;
      case 'fillColor':
        setFillColor(value);
        break;
    }
    saveState();
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setError('');
    setLoading(true);

    try {
      if (file.type !== 'application/pdf') {
        throw new Error('Please upload a PDF file');
      }

      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size should be less than 10MB');
      }

      const arrayBuffer = await file.arrayBuffer();
      const pdfDoc = await PDFDocument.load(arrayBuffer);
      
      setPdfDoc(pdfDoc);
      setTotalPages(pdfDoc.getPageCount());
      setCurrentPage(1);
      setScale(1);
      setFileName(file.name);
      saveState();
    } catch (err) {
      console.error('Error loading PDF:', err);
      setError(err instanceof Error ? err.message : 'Failed to load PDF');
      setPdfDoc(null);
      setFileName('');
    } finally {
      setLoading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const createNewPdf = async () => {
    setError('');
    try {
      const newPdfDoc = await PDFDocument.create();
      const page = newPdfDoc.addPage([612, 792]); // US Letter size
      setPdfDoc(newPdfDoc);
      setTotalPages(1);
      setCurrentPage(1);
      setFileName('new-document.pdf');
      saveState();
    } catch (err) {
      console.error('Error creating new PDF:', err);
      setError('Failed to create new PDF');
    }
  };

  const handleTextUpdate = (index: number, newText: string) => {
    saveState();
    // Implement text update functionality
    console.log(`Text at index ${index} updated to: ${newText}`);
  };

  const downloadPdf = async () => {
    if (!pdfDoc) return;
    
    try {
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading PDF:', err);
      setError('Failed to download PDF');
    }
  };

  const savePdf = async () => {
    if (!pdfDoc) return;

    try {
      const pdfBytes = await pdfDoc.save();
      const response = await fetch('/api/pdf/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: fileName || 'document.pdf',
          content: Array.from(pdfBytes),
        }),
      });

      if (!response.ok) throw new Error('Failed to save PDF');
      setError('');
      setIsModified(false);

      // Show success message
      alert('PDF saved successfully!');
    } catch (err) {
      console.error('Error saving PDF:', err);
      setError('Failed to save PDF');
      alert('Failed to save PDF. Please try again.');
    }
  };

  return (
    <div className="h-screen flex flex-col">
      <div className="border-b p-4 flex justify-between items-center">
        <h1 className="text-2xl font-bold">PDF Editor</h1>
        <div className="flex gap-4">
          <Button 
            onClick={undo} 
            variant="outline" 
            disabled={undoStack.length === 0}
          >
            <Undo2 className="w-4 h-4 mr-2" />
            Undo
          </Button>
          <Button 
            onClick={redo} 
            variant="outline" 
            disabled={redoStack.length === 0}
          >
            <Redo2 className="w-4 h-4 mr-2" />
            Redo
          </Button>
          <Button onClick={createNewPdf} variant="outline">
            <Plus className="w-4 h-4 mr-2" />
            New PDF
          </Button>
          <Button 
            onClick={() => fileInputRef.current?.click()} 
            variant="outline" 
            disabled={loading}
          >
            <Upload className="w-4 h-4 mr-2" />
            {loading ? 'Loading...' : 'Upload PDF'}
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            accept="application/pdf"
            className="hidden"
            onChange={handleFileUpload}
            disabled={loading}
          />
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="flex-1 flex">
        <PdfSidebar
          pdfDoc={pdfDoc}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
        />

        <div className="flex-1 flex flex-col">
          <PdfToolbar
            pdfDoc={pdfDoc}
            currentPage={currentPage}
            totalPages={totalPages}
            scale={scale}
            setScale={setScale}
            onSave={savePdf}
            onDownload={downloadPdf}
            fileName={fileName}
            currentTool={currentTool}
            setCurrentTool={setCurrentTool}
            isModified={isModified}
          />
          <div className="flex-1 overflow-auto bg-gray-100 flex items-center justify-center">
            <PdfCanvas
              pdfDoc={pdfDoc}
              currentPage={currentPage}
              scale={scale}
              currentTool={currentTool}
              onTextUpdate={handleTextUpdate}
              onModification={saveState}
              fontSize={fontSize}
              color={color}
              brushSize={brushSize}
              borderWidth={borderWidth}
              fillColor={fillColor}
              selectedElement={selectedElement}
              setSelectedElement={setSelectedElement}
              textContent={textContent}
            />
          </div>
        </div>

        <PdfEditSidebar
          currentTool={currentTool}
          setCurrentTool={setCurrentTool}
          onPropertyChange={handlePropertyChange}
          fontSize={fontSize}
          color={color}
          brushSize={brushSize}
          borderWidth={borderWidth}
          fillColor={fillColor}
          textContent={textContent}
          setTextContent={setTextContent}
        />
      </div>
    </div>
  );
}
