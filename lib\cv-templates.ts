export interface CvTemplate {
  id: string;
  name: string;
  layout: string;
}

export const cvTemplates: CvTemplate[] = [
  {
    id: 'modern',
    name: 'Modern',
    layout: 'modern',
  },
  {
    id: 'classic',
    name: 'Classic',
    layout: 'classic',
  },
  {
    id: 'minimal',
    name: 'Minimal',
    layout: 'minimal',
  },
];

export interface CvData {
  template: string;
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    address: string;
    summary: string;
  };
  education: Array<{
    id: string;
    degree: string;
    institution: string;
    year: string;
  }>;
  experience: Array<{
    id: string;
    position: string;
    company: string;
    startDate: string;
    endDate: string;
    description: string;
  }>;
  skills: Array<{
    id: string;
    name: string;
    level: string;
  }>;
}

export const defaultCvData: CvData = {
  template: 'modern',
  personalInfo: {
    fullName: '',
    email: '',
    phone: '',
    address: '',
    summary: '',
  },
  education: [],
  experience: [],
  skills: [],
};