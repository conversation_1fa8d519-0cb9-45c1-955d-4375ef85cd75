import PdfCanvas from '@/components/pdf-editor/PdfCanvas';
import PdfSidebar from '@/components/pdf-editor/PdfSidebar';
import PdfToolbar from '@/components/pdf-editor/PdfToolbar';
import { useState, useCallback, useEffect } from 'react';
import { PDFDocument } from 'pdf-lib';
import { Button } from '@/components/ui/button';
import { Undo2, Redo2, Trash2 } from 'lucide-react';

interface DrawingPath {
  id: string;
  type: string;
  points: { x: number; y: number }[];
  color: string;
  size: number;
  fillColor?: string;
  borderWidth?: number;
  text?: string;
}

interface EditorState {
  pdfDoc: PDFDocument | null;
  currentPage: number;
  scale: number;
  currentTool: string;
}

const PdfEditor: React.FC = () => {
  const [pdfDoc, setPdfDoc] = useState<PDFDocument | null>(null);
  const [currentTool, setCurrentTool] = useState<string>('');
  const [scale, setScale] = useState<number>(1);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [fileName, setFileName] = useState<string>('');
  const [undoStack, setUndoStack] = useState<EditorState[]>([]);
  const [redoStack, setRedoStack] = useState<EditorState[]>([]);
  const [isModified, setIsModified] = useState(false);
  const [selectedElement, setSelectedElement] = useState<DrawingPath | null>(null);
  const [textContent, setTextContent] = useState('');
  const [drawings, setDrawings] = useState<DrawingPath[]>([]);
  const [fontSize, setFontSize] = useState(16);
  const [color, setColor] = useState('#000000');
  const [brushSize, setBrushSize] = useState(2);
  const [borderWidth, setBorderWidth] = useState(1);
  const [fillColor, setFillColor] = useState('#ffffff');

  const saveState = useCallback(() => {
    const currentState: EditorState = {
      pdfDoc,
      currentPage,
      scale,
      currentTool,
    };
    setUndoStack(prev => [...prev, currentState]);
    setRedoStack([]);
    setIsModified(true);
  }, [pdfDoc, currentPage, scale, currentTool]);

  const undo = () => {
    if (undoStack.length === 0) return;
    
    const previousState = undoStack[undoStack.length - 1];
    const currentState: EditorState = {
      pdfDoc,
      currentPage,
      scale,
      currentTool,
    };
    
    setRedoStack(prev => [...prev, currentState]);
    setUndoStack(prev => prev.slice(0, -1));
    
    setPdfDoc(previousState.pdfDoc);
    setCurrentPage(previousState.currentPage);
    setScale(previousState.scale);
    setCurrentTool(previousState.currentTool);
  };

  const redo = () => {
    if (redoStack.length === 0) return;
    
    const nextState = redoStack[redoStack.length - 1];
    const currentState: EditorState = {
      pdfDoc,
      currentPage,
      scale,
      currentTool,
    };
    
    setUndoStack(prev => [...prev, currentState]);
    setRedoStack(prev => prev.slice(0, -1));
    
    setPdfDoc(nextState.pdfDoc);
    setCurrentPage(nextState.currentPage);
    setScale(nextState.scale);
    setCurrentTool(nextState.currentTool);
  };

  const handleTextUpdate = async (index: number, newText: string) => {
    if (!pdfDoc) return;
    saveState();
    // Implementation for text update
    console.log(`Text at index ${index} updated to: ${newText}`);
  };

  const handleSave = async () => {
    if (!pdfDoc) return;
    try {
      const pdfBytes = await pdfDoc.save();
      const response = await fetch('/api/pdf/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName,
          content: Array.from(pdfBytes),
        }),
      });

      if (!response.ok) throw new Error('Failed to save PDF');
      setIsModified(false);
    } catch (error) {
      console.error('Error saving PDF:', error);
    }
  };

  const handleDownload = async () => {
    if (!pdfDoc) return;
    try {
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF:', error);
    }
  };

  const deleteSelectedElement = () => {
    if (!selectedElement) return;
    
    saveState(); // Save current state for undo
    setDrawings(drawings.filter(d => d.id !== selectedElement.id));
    setSelectedElement(null);
    setIsModified(true);
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Delete' || e.key === 'Backspace') {
      deleteSelectedElement();
    }
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedElement]);

  return (
    <div className="h-screen flex flex-col">
      <div className="flex-1 flex overflow-hidden">
        {/* History Controls */}
        <div className="absolute top-4 left-4 flex gap-2 z-10">
          <Button
            variant="outline"
            size="icon"
            onClick={undo}
            disabled={undoStack.length === 0}
          >
            <Undo2 className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={redo}
            disabled={redoStack.length === 0}
          >
            <Redo2 className="h-4 w-4" />
          </Button>
        </div>

        {/* Sidebar */}
        <PdfSidebar
          pdfDoc={pdfDoc}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
        />

        {/* Main Editor Area */}
        <div className="flex-1 flex flex-col">
          <PdfToolbar
            pdfDoc={pdfDoc}
            currentPage={currentPage}
            totalPages={totalPages}
            scale={scale}
            setScale={setScale}
            onSave={handleSave}
            onDownload={handleDownload}
            fileName={fileName}
            currentTool={currentTool}
            setCurrentTool={setCurrentTool}
            isModified={isModified}
          />
          <div className="flex-1 overflow-auto bg-gray-100 flex items-center justify-center">
            <PdfCanvas
              pdfDoc={pdfDoc}
              currentPage={currentPage}
              scale={scale}
              currentTool={currentTool}
              onTextUpdate={handleTextUpdate}
              onModification={() => setIsModified(true)}
              fontSize={fontSize}
              color={color}
              brushSize={brushSize}
              borderWidth={borderWidth}
              fillColor={fillColor}
              selectedElement={selectedElement}
              setSelectedElement={setSelectedElement}
              textContent={textContent}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PdfEditor;
