import { NextResponse } from 'next/server';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import { CvData } from '@/lib/cv-templates';

export async function POST(request: Request) {
  try {
    const data: CvData = await request.json();
    
    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([612, 792]); // US Letter size
    const { width, height } = page.getSize();
    
    // Load fonts
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
    const regularFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    
    let yPosition = height - 50;
    const margin = 50;
    const lineHeight = 20;
    
    // Helper function to add text
    const addText = (text: string, x: number, y: number, options: any = {}) => {
      page.drawText(text, {
        x,
        y,
        size: options.size || 12,
        font: options.font || regularFont,
        color: options.color || rgb(0, 0, 0),
        ...options
      });
    };
    
    // Helper function to add section header
    const addSectionHeader = (title: string) => {
      yPosition -= lineHeight * 1.5;
      addText(title, margin, yPosition, {
        font: boldFont,
        size: 16,
        color: rgb(0.2, 0.2, 0.2)
      });
      yPosition -= lineHeight * 0.5;
      
      // Add underline
      page.drawLine({
        start: { x: margin, y: yPosition },
        end: { x: width - margin, y: yPosition },
        thickness: 1,
        color: rgb(0.8, 0.8, 0.8)
      });
      yPosition -= lineHeight;
    };
    
    // Header - Name and Contact Info
    addText(data.personalInfo.fullName, margin, yPosition, {
      font: boldFont,
      size: 24,
      color: rgb(0.1, 0.1, 0.1)
    });
    yPosition -= lineHeight * 1.5;
    
    const contactInfo = [
      data.personalInfo.email,
      data.personalInfo.phone,
      data.personalInfo.address
    ].filter(Boolean).join(' • ');
    
    addText(contactInfo, margin, yPosition, {
      size: 11,
      color: rgb(0.4, 0.4, 0.4)
    });
    yPosition -= lineHeight * 2;
    
    // Professional Summary
    if (data.personalInfo.summary) {
      addSectionHeader('Professional Summary');
      
      // Word wrap for summary
      const words = data.personalInfo.summary.split(' ');
      let line = '';
      const maxWidth = width - (margin * 2);
      
      for (const word of words) {
        const testLine = line + (line ? ' ' : '') + word;
        const textWidth = regularFont.widthOfTextAtSize(testLine, 12);
        
        if (textWidth > maxWidth && line) {
          addText(line, margin, yPosition);
          yPosition -= lineHeight;
          line = word;
        } else {
          line = testLine;
        }
      }
      
      if (line) {
        addText(line, margin, yPosition);
        yPosition -= lineHeight;
      }
    }
    
    // Experience
    if (data.experience.length > 0) {
      addSectionHeader('Experience');
      
      for (const exp of data.experience) {
        if (yPosition < 100) {
          // Add new page if needed
          const newPage = pdfDoc.addPage([612, 792]);
          yPosition = height - 50;
        }
        
        addText(exp.position, margin, yPosition, {
          font: boldFont,
          size: 14
        });
        yPosition -= lineHeight;
        
        addText(exp.company, margin, yPosition, {
          size: 12,
          color: rgb(0.3, 0.3, 0.3)
        });
        yPosition -= lineHeight * 0.8;
        
        const dateRange = `${exp.startDate} - ${exp.endDate}`;
        addText(dateRange, margin, yPosition, {
          size: 10,
          color: rgb(0.5, 0.5, 0.5)
        });
        yPosition -= lineHeight;
        
        if (exp.description) {
          // Word wrap for description
          const words = exp.description.split(' ');
          let line = '';
          const maxWidth = width - (margin * 2);
          
          for (const word of words) {
            const testLine = line + (line ? ' ' : '') + word;
            const textWidth = regularFont.widthOfTextAtSize(testLine, 11);
            
            if (textWidth > maxWidth && line) {
              addText(line, margin, yPosition, { size: 11 });
              yPosition -= lineHeight * 0.9;
              line = word;
            } else {
              line = testLine;
            }
          }
          
          if (line) {
            addText(line, margin, yPosition, { size: 11 });
            yPosition -= lineHeight * 0.9;
          }
        }
        
        yPosition -= lineHeight * 0.5;
      }
    }
    
    // Education
    if (data.education.length > 0) {
      addSectionHeader('Education');
      
      for (const edu of data.education) {
        if (yPosition < 100) {
          // Add new page if needed
          const newPage = pdfDoc.addPage([612, 792]);
          yPosition = height - 50;
        }
        
        addText(edu.degree, margin, yPosition, {
          font: boldFont,
          size: 12
        });
        yPosition -= lineHeight;
        
        addText(edu.institution, margin, yPosition, {
          size: 11,
          color: rgb(0.3, 0.3, 0.3)
        });
        yPosition -= lineHeight * 0.8;
        
        addText(edu.year, margin, yPosition, {
          size: 10,
          color: rgb(0.5, 0.5, 0.5)
        });
        yPosition -= lineHeight * 1.2;
      }
    }
    
    // Skills
    if (data.skills.length > 0) {
      addSectionHeader('Skills');
      
      const skillsPerRow = 2;
      const columnWidth = (width - (margin * 2)) / skillsPerRow;
      
      for (let i = 0; i < data.skills.length; i += skillsPerRow) {
        if (yPosition < 100) {
          // Add new page if needed
          const newPage = pdfDoc.addPage([612, 792]);
          yPosition = height - 50;
        }
        
        for (let j = 0; j < skillsPerRow && i + j < data.skills.length; j++) {
          const skill = data.skills[i + j];
          const x = margin + (j * columnWidth);
          
          addText(skill.name, x, yPosition, {
            font: boldFont,
            size: 11
          });
          
          const skillNameWidth = boldFont.widthOfTextAtSize(skill.name, 11);
          addText(skill.level, x + skillNameWidth + 10, yPosition, {
            size: 11,
            color: rgb(0.5, 0.5, 0.5)
          });
        }
        
        yPosition -= lineHeight;
      }
    }
    
    // Save the PDF
    const pdfBytes = await pdfDoc.save();
    
    return new NextResponse(pdfBytes, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename=${data.personalInfo.fullName.toLowerCase().replace(/\s+/g, '-')}-cv.pdf`,
      },
    });
  } catch (error) {
    console.error('Error generating PDF:', error);
    return NextResponse.json({ error: 'Failed to generate PDF' }, { status: 500 });
  }
}
