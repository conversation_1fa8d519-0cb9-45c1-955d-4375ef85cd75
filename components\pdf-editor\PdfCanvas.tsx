import React, { useEffect, useRef, useState } from 'react';
import { PDFDocument } from 'pdf-lib';
import { PDFJS } from '@/lib/pdf-setup';

interface PdfCanvasProps {
  pdfDoc: PDFDocument | null;
  currentPage: number;
  scale: number;
  currentTool: string;
  onTextUpdate?: (index: number, newText: string) => void;
  onModification?: () => void;
  fontSize: number;
  color: string;
  brushSize: number;
  borderWidth: number;
  fillColor: string;
  selectedElement: DrawingPath | null;
  setSelectedElement: (element: DrawingPath | null) => void;
  textContent: string; // Add this prop
}

interface DrawingPath {
  id: string;
  type: string;
  points: { x: number; y: number }[];
  color: string;
  size: number;
  fillColor?: string;
  borderWidth?: number;
  text?: string; // Add this prop
}

const PdfCanvas: React.FC<PdfCanvasProps> = ({
  pdfDoc,
  currentPage,
  scale,
  currentTool,
  onTextUpdate,
  onModification,
  fontSize,
  color,
  brushSize,
  borderWidth,
  fillColor,
  selectedElement,
  setSelectedElement,
  textContent, // Add this prop
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const contextRef = useRef<CanvasRenderingContext2D | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentPath, setCurrentPath] = useState<DrawingPath | null>(null);
  const [drawings, setDrawings] = useState<DrawingPath[]>([]);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [baseCanvas, setBaseCanvas] = useState<HTMLCanvasElement | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    if (!context) return;

    contextRef.current = context;
    context.lineCap = 'round';
    context.lineJoin = 'round';
    
    renderPdf();
  }, [pdfDoc, currentPage, scale]);

  useEffect(() => {
    renderDrawings();
  }, [drawings]);

  const renderPdf = async () => {
    if (!pdfDoc || !canvasRef.current || !contextRef.current) return;

    try {
      const pdfBytes = await pdfDoc.save();
      const pdf = await PDFJS.getDocument({ data: pdfBytes }).promise;
      const page = await pdf.getPage(currentPage);
      
      const viewport = page.getViewport({ scale });
      const canvas = canvasRef.current;
      canvas.width = viewport.width;
      canvas.height = viewport.height;

      // Create a temporary canvas for the base PDF
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = viewport.width;
      tempCanvas.height = viewport.height;
      const tempContext = tempCanvas.getContext('2d');
      
      if (!tempContext) return;

      // Render PDF to temporary canvas
      await page.render({
        canvasContext: tempContext,
        viewport,
      }).promise;

      setBaseCanvas(tempCanvas);
      
      // Draw base PDF and all drawings
      contextRef.current.clearRect(0, 0, canvas.width, canvas.height);
      contextRef.current.drawImage(tempCanvas, 0, 0);
      renderDrawings();
    } catch (error) {
      console.error('Error rendering PDF:', error);
    }
  };

  const renderDrawings = () => {
    if (!contextRef.current || !canvasRef.current || !baseCanvas) return;
    const ctx = contextRef.current;
    const canvas = canvasRef.current;

    // Clear and redraw base PDF
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(baseCanvas, 0, 0);

    // Draw all completed drawings
    drawings.forEach(path => {
      ctx.beginPath();
      ctx.strokeStyle = path.color;
      ctx.lineWidth = path.size;
      ctx.fillStyle = path.fillColor || 'transparent';

      // Draw the element
      drawPath(ctx, path);

      // If element is selected, draw selection indicator
      if (selectedElement?.id === path.id) {
        ctx.save();
        ctx.strokeStyle = '#2196F3';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        
        if (path.type === 'rectangle') {
          const [start, end] = path.points;
          const width = end.x - start.x;
          const height = end.y - start.y;
          ctx.strokeRect(start.x - 5, start.y - 5, width + 10, height + 10);
        } else if (path.type === 'circle') {
          const [center] = path.points;
          const radius = Math.sqrt(
            Math.pow(path.points[1].x - center.x, 2) + 
            Math.pow(path.points[1].y - center.y, 2)
          );
          ctx.beginPath();
          ctx.arc(center.x, center.y, radius + 5, 0, 2 * Math.PI);
          ctx.stroke();
        } else if (path.type === 'draw') {
          // Create bounding box for freehand drawing
          const minX = Math.min(...path.points.map(p => p.x));
          const maxX = Math.max(...path.points.map(p => p.x));
          const minY = Math.min(...path.points.map(p => p.y));
          const maxY = Math.max(...path.points.map(p => p.y));
          ctx.strokeRect(minX - 5, minY - 5, maxX - minX + 10, maxY - minY + 10);
        }
        ctx.restore();
      }
    });

    // Draw current path if exists
    if (currentPath) {
      drawPath(ctx, currentPath);
    }
  };

  const drawPath = (ctx: CanvasRenderingContext2D, path: DrawingPath) => {
    if (path.type === 'draw') {
      path.points.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y);
        } else {
          ctx.lineTo(point.x, point.y);
        }
      });
      ctx.stroke();
    } else if (path.type === 'rectangle') {
      const start = path.points[0];
      const end = path.points[1];
      const width = end.x - start.x;
      const height = end.y - start.y;
      
      ctx.lineWidth = path.borderWidth || borderWidth;
      ctx.strokeRect(start.x, start.y, width, height);
      ctx.fillRect(start.x, start.y, width, height);
    } else if (path.type === 'circle') {
      const start = path.points[0];
      const end = path.points[1];
      const radius = Math.sqrt(
        Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2)
      );
      
      ctx.lineWidth = path.borderWidth || borderWidth;
      ctx.beginPath();
      ctx.arc(start.x, start.y, radius, 0, 2 * Math.PI);
      ctx.fill();
      ctx.stroke();
    }
  };

  const getMousePos = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  };

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!currentTool) return;

    const pos = getMousePos(e);
    setIsDrawing(true);
    setStartPoint(pos);

    const newPath: DrawingPath = {
      id: Date.now().toString(), // Add unique id using timestamp
      type: currentTool,
      points: [pos],
      color: color,
      size: currentTool === 'draw' ? brushSize : borderWidth,
      fillColor: currentTool !== 'draw' ? fillColor : undefined,
      borderWidth: currentTool !== 'draw' ? borderWidth : undefined,
    };

    setCurrentPath(newPath);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !currentPath || !contextRef.current) return;

    const pos = getMousePos(e);

    if (currentTool === 'draw') {
      setCurrentPath(prev => {
        if (!prev) return null;
        return {
          ...prev,
          points: [...prev.points, pos],
        };
      });
    } else {
      setCurrentPath(prev => {
        if (!prev) return null;
        return {
          ...prev,
          points: [prev.points[0], pos],
        };
      });
    }

    renderDrawings();
  };

  const endDrawing = () => {
    if (!isDrawing || !currentPath) return;
    
    setDrawings(prev => [...prev, currentPath]);
    setCurrentPath(null);
    setIsDrawing(false);
    setStartPoint(null);
    
    if (onModification) {
      onModification();
    }
  };

  const handleTextTool = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (currentTool !== 'text' || !textContent?.trim()) return; // Add null check with ?

    const pos = getMousePos(e);
    
    if (contextRef.current) {
      const ctx = contextRef.current;
      ctx.font = `${fontSize}px Arial`;
      ctx.fillStyle = color;
      ctx.fillText(textContent, pos.x, pos.y);

      // Add text as a drawing element
      const newDrawing: DrawingPath = {
        id: Date.now().toString(),
        type: 'text',
        points: [pos],
        color: color,
        size: fontSize,
        text: textContent,
      };
      setDrawings(prev => [...prev, newDrawing]);
      
      if (onModification) {
        onModification();
      }
    }
  };

  const isPointInPath = (point: { x: number; y: number }, path: DrawingPath) => {
    if (path.type === 'rectangle') {
      const [start, end] = path.points;
      const minX = Math.min(start.x, end.x);
      const maxX = Math.max(start.x, end.x);
      const minY = Math.min(start.y, end.y);
      const maxY = Math.max(start.y, end.y);
      
      return point.x >= minX && point.x <= maxX && point.y >= minY && point.y <= maxY;
    } else if (path.type === 'circle') {
      const [center] = path.points;
      const radius = Math.sqrt(
        Math.pow(path.points[1].x - center.x, 2) + 
        Math.pow(path.points[1].y - center.y, 2)
      );
      const distance = Math.sqrt(
        Math.pow(point.x - center.x, 2) + 
        Math.pow(point.y - center.y, 2)
      );
      return distance <= radius;
    } else if (path.type === 'draw') {
      // Simplified hit detection for freehand drawing
      return path.points.some(pathPoint => 
        Math.abs(point.x - pathPoint.x) < 5 && 
        Math.abs(point.y - pathPoint.y) < 5
      );
    }
    return false;
  };

  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (currentTool) return; // Don't select while using tools
    
    const pos = getMousePos(e);
    
    // Search in reverse to select top-most element
    for (let i = drawings.length - 1; i >= 0; i--) {
      const drawing = drawings[i];
      if (isPointInPath(pos, drawing)) {
        setSelectedElement(drawing);
        renderDrawings(); // Redraw to show selection
        return;
      }
    }
    
    setSelectedElement(null);
  };

  return (
    <canvas
      ref={canvasRef}
      className="border shadow-lg"
      onClick={currentTool === 'text' ? handleTextTool : handleCanvasClick}
      onMouseDown={currentTool !== 'text' ? startDrawing : undefined}
      onMouseMove={currentTool !== 'text' ? draw : undefined}
      onMouseUp={currentTool !== 'text' ? endDrawing : undefined}
      onMouseLeave={currentTool !== 'text' ? endDrawing : undefined}
    />
  );
};

export default PdfCanvas;
