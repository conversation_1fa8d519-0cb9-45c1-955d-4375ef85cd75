import { NextResponse } from 'next/server';
import { Document, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType } from 'docx';
import { CvData } from '@/lib/cv-templates';

export async function POST(request: Request) {
  try {
    const data: CvData = await request.json();
    
    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          // Header with name and contact info
          new Paragraph({
            text: data.personalInfo.fullName,
            heading: HeadingLevel.TITLE,
            alignment: 'center',
          }),
          new Paragraph({
            text: [data.personalInfo.email, data.personalInfo.phone, data.personalInfo.address].join(' • '),
            alignment: 'center',
          }),
          
          // Summary
          new Paragraph({
            text: 'Professional Summary',
            heading: HeadingLevel.HEADING_1,
            spacing: { before: 400, after: 200 },
          }),
          new Paragraph({
            text: data.personalInfo.summary,
          }),

          // Experience
          new Paragraph({
            text: 'Experience',
            heading: HeadingLevel.HEADING_1,
            spacing: { before: 400, after: 200 },
          }),
          ...data.experience.map(exp => [
            new Paragraph({
              text: exp.position,
              heading: HeadingLevel.HEADING_2,
            }),
            new Paragraph({
              text: `${exp.company} (${exp.startDate} - ${exp.endDate})`,
              spacing: { before: 100, after: 100 },
            }),
            new Paragraph({
              text: exp.description,
              spacing: { after: 200 },
            }),
          ]).flat(),

          // Education
          new Paragraph({
            text: 'Education',
            heading: HeadingLevel.HEADING_1,
            spacing: { before: 400, after: 200 },
          }),
          ...data.education.map(edu => [
            new Paragraph({
              text: edu.degree,
              heading: HeadingLevel.HEADING_2,
            }),
            new Paragraph({
              text: `${edu.institution} (${edu.year})`,
              spacing: { after: 200 },
            }),
          ]).flat(),

          // Skills
          new Paragraph({
            text: 'Skills',
            heading: HeadingLevel.HEADING_1,
            spacing: { before: 400, after: 200 },
          }),
          new Table({
            width: {
              size: 100,
              type: WidthType.PERCENTAGE,
            },
            rows: data.skills.map(skill => new TableRow({
              children: [
                new TableCell({
                  children: [new Paragraph({ text: skill.name })],
                }),
                new TableCell({
                  children: [new Paragraph({ text: skill.level })],
                }),
              ],
            })),
          }),
        ],
      }],
    });

    const buffer = await doc.save();
    
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': `attachment; filename=${data.personalInfo.fullName.toLowerCase().replace(/\s+/g, '-')}-cv.docx`,
      },
    });
  } catch (error) {
    console.error('Error generating DOCX:', error);
    return NextResponse.json({ error: 'Failed to generate DOCX' }, { status: 500 });
  }
}